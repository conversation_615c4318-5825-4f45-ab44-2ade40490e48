//
//  YYBMainNavigationView.m
//  YYBMacApp
//
//  Created by halehuang on 2025/8/13.
//

#import "YYBMainNavigationView.h"
#import "Masonry.h"
#import "YYBMainNavigationItemView.h"
#import "YYBMainNavigationModel.h"
#import "YYBMainNavigationItem.h"

@interface YYBMainNavigationView () <YYBMainNavigationItemViewDelegate>

@property (nonatomic, strong) NSVisualEffectView *visualEffectView;
@property (nonatomic, strong) NSImageView *bottomImageView;
@property (nonatomic, strong) YYBMainNavigationModel *navigationModel;
@property (nonatomic, strong) NSMutableArray<YYBMainNavigationItemView *> *itemViews;

@end

@implementation YYBMainNavigationView

#pragma mark - 初始化方法

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        _itemViews = [NSMutableArray array];
        [self setupView];
    }
    return self;
}

- (void)setDataSource:(id<YYBMainNavigationViewDataSource>)dataSource {
    _dataSource = dataSource;
    [self reloadData];
}

- (void)setupView {
    self.wantsLayer = YES;
    self.layer.backgroundColor = [NSColor clearColor].CGColor;
    
    // 创建背景图
    self.visualEffectView = [[NSVisualEffectView alloc] initWithFrame:NSZeroRect];
    self.visualEffectView.material = NSVisualEffectMaterialSidebar;
    self.visualEffectView.blendingMode = NSVisualEffectBlendingModeBehindWindow;
    self.visualEffectView.state = NSVisualEffectStateFollowsWindowActiveState;
    self.visualEffectView.wantsLayer = YES;
    [self addSubview:self.visualEffectView];
    [self.visualEffectView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
}

- (void)reloadData {
    if ([self.dataSource respondsToSelector:@selector(navigationModelForNavigationView:)]) {
        self.navigationModel = [self.dataSource navigationModelForNavigationView:self];
    }
    
    [self removeAllNavigationItems];
    [self addNavigationItems];
}

- (void)addNavigationItems {
    CGFloat size = 44.0;
    NSArray *visibleItems = [self.navigationModel visibleItems];
    
    for (NSInteger i = 0; i < visibleItems.count; i++) {
        YYBMainNavigationItem *item = visibleItems[i];
        YYBMainNavigationItemView *itemView = [[YYBMainNavigationItemView alloc] initWithItem:item];
        itemView.delegate = self;
        
        [self.visualEffectView addSubview:itemView];
        [self.itemViews addObject:itemView];
        
        [itemView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.visualEffectView);
            make.top.equalTo(self.visualEffectView).offset(88 + i * (12 + size));
            make.size.mas_equalTo(CGSizeMake(size, size));
        }];
    }
}

- (void)removeAllNavigationItems {
    for (YYBMainNavigationItemView *itemView in self.itemViews) {
        [itemView removeFromSuperview];
    }
    [self.itemViews removeAllObjects];
}

- (void)selectItem:(YYBMainNavigationItem *)item {
    if (item.isSelected) {
        return;
    }
    
    if ([self.delegate respondsToSelector:@selector(navigationView:didSelectItem:)]) {
        [self.delegate navigationView:self didSelectItem:item];
    }
}

- (void)updateSelectedItem:(YYBMainNavigationItem *)item {
    [self.navigationModel selectItem:item];
    for (YYBMainNavigationItemView *view in self.itemViews) {
        [view updateView];
    }
}

#pragma mark - YYBMainNavigationItemViewDelegate

- (void)navigationItemViewDidClick:(YYBMainNavigationItemView *)itemView {
    YYBMainNavigationItem *clickedItem = itemView.item;
    [self selectItem:clickedItem];
}

@end
