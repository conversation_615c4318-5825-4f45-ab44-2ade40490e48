//
//  InstallApkInfo.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/18.
//

#import <Foundation/Foundation.h>
#import "YYModel.h"

@class InstallApkExtendInfo;

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, InstallApkStatus) {
    InstallApkStatusIdle = 0,                 ///< 空闲未安装
    InstallApkStatusStartInstalling,          ///< 开始安装
    InstallApkStatusInstalling,               ///< 安装中
    InstallApkStatusInstallCompleted,         ///< 安装完成/已安装
    InstallApkStatusInstallError,             ///< 安装失败
    InstallApkStatusStartUninstalling,        ///< 开始卸载
    InstallApkStatusUninstalling,             ///< 卸载中
    InstallApkStatusUninstallCompleted,       ///< 卸载完成/已卸载
    InstallApkStatusUninstallError,           ///< 卸载错误
    InstallApkStatusUninstallCancelled,       ///< 卸载取消
};

// 安装apk信息
@interface InstallApkInfo : NSObject<YYModel>

@property (nonatomic, strong) NSString *pkgName;
@property (nonatomic, strong) NSString *name;
@property (nonatomic, strong) NSString *iconUrl;
@property (nonatomic, strong) NSString *filePath;
@property (nonatomic, strong) NSString *iconPath;
@property (nonatomic, strong) NSString *versionName;
@property (nonatomic, strong) NSString *md5;
@property (nonatomic, assign) NSInteger size;
@property (nonatomic, assign) BOOL isInstallInEngine; // 是否在引擎内安装
@property (nonatomic, assign) BOOL hasShotcut; // 是否有快捷方式
@property (nonatomic, strong) InstallApkExtendInfo *extendInfo;
@property (nonatomic, assign) BOOL silentUpdate;          // 是否是静默升级，默认为NO

// 安装/卸载状态
@property (nonatomic, assign) InstallApkStatus installStatus;
// 最近一次操作的错误码
@property (nonatomic, assign) NSInteger lastErrorCode;
// 最近一次操作的错误描述
@property (nonatomic, strong, nullable) NSString *lastErrorMessage;

@end

NS_ASSUME_NONNULL_END
