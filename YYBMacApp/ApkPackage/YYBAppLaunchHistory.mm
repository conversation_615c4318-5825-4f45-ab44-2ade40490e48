//
//  YYBAppLaunchHistory.mm
//  YYBMacApp
//
//  Created by liche<PERSON><PERSON> on 2025/8/20.
//

#import "YYBAppLaunchHistory.h"
#import "InstallApkInfo.h"
#import <YYBMacFusionSDK/YYBMacMMKV.h>
#import <YYBMacFusionSDK/YYBMacLog.h>

static NSString *const kTag = @"YYBAppLaunchHistory";
static NSString *const kLaunchHistoryKey = @"YYBAppLaunchHistory";
static NSString *const kMaxHistoryCount = @"500"; // 最大保存500条记录

@implementation YYBAppLaunchHistoryItem

+ (instancetype)itemWithApkInfo:(InstallApkInfo *)apkInfo {
    YYBAppLaunchHistoryItem *item = [[YYBAppLaunchHistoryItem alloc] init];
    item.pkgName = apkInfo.pkgName ?: @"";
    item.appName = apkInfo.name ?: @"";
    item.iconUrl = apkInfo.iconUrl ?: @"";
    item.launchTime = [NSDate date];
    return item;
}

+ (instancetype)itemWithDictionary:(NSDictionary *)dict {
    YYBAppLaunchHistoryItem *item = [[YYBAppLaunchHistoryItem alloc] init];
    item.pkgName = dict[@"pkgName"] ?: @"";
    item.appName = dict[@"appName"] ?: @"";
    item.iconUrl = dict[@"iconUrl"] ?: @"";
    
    NSTimeInterval timestamp = [dict[@"launchTime"] doubleValue];
    item.launchTime = timestamp > 0 ? [NSDate dateWithTimeIntervalSince1970:timestamp] : [NSDate date];
    
    return item;
}

- (NSDictionary *)toDictionary {
    return @{
        @"pkgName": self.pkgName ?: @"",
        @"appName": self.appName ?: @"",
        @"iconUrl": self.iconUrl ?: @"",
        @"launchTime": @([self.launchTime timeIntervalSince1970]),
    };
}

- (NSString *)description {
    return [NSString stringWithFormat:@"<YYBAppLaunchHistoryItem: pkgName=%@, appName=%@, launchTime=%@>",
            self.pkgName, self.appName, self.launchTime];
}

@end

@implementation YYBAppLaunchHistory

+ (void)recordAppLaunch:(InstallApkInfo *)apkInfo {
    if (!apkInfo || !apkInfo.pkgName.length) {
        YYBMacLogError(kTag, @"Invalid apkInfo for recording launch history");
        return;
    }
    
    @try {
        NSMutableArray<YYBAppLaunchHistoryItem *> *historyList = [[self getLaunchHistoryFromStorage] mutableCopy];
        
        // 查找是否已存在该App的记录
        YYBAppLaunchHistoryItem *existingItem = nil;
        NSInteger existingIndex = -1;
        for (NSInteger i = 0; i < historyList.count; i++) {
            YYBAppLaunchHistoryItem *item = historyList[i];
            if ([item.pkgName isEqualToString:apkInfo.pkgName]) {
                existingItem = item;
                existingIndex = i;
                break;
            }
        }
        
        if (existingItem) {
            // 更新现有记录
            existingItem.launchTime = [NSDate date];
            existingItem.appName = apkInfo.name ?: existingItem.appName; // 更新可能变化的应用名
            existingItem.iconUrl = apkInfo.iconUrl ?: existingItem.iconUrl; // 更新可能变化的图标
            
            // 移动到列表开头（最新）
            [historyList removeObjectAtIndex:existingIndex];
            [historyList insertObject:existingItem atIndex:0];
            
        } else {
            // 创建新记录
            YYBAppLaunchHistoryItem *newItem = [YYBAppLaunchHistoryItem itemWithApkInfo:apkInfo];
            [historyList insertObject:newItem atIndex:0];
        }
        
        // 限制历史记录数量
        NSInteger maxCount = [kMaxHistoryCount integerValue];
        if (historyList.count > maxCount) {
            [historyList removeObjectsInRange:NSMakeRange(maxCount, historyList.count - maxCount)];
        }
        
        // 保存到本地
        [self saveHistoryToStorage:historyList];
        
        YYBMacLogInfo(kTag, @"[Recorded app launch] pkgName: %@ name: %@", apkInfo.pkgName, apkInfo.name);
        
    } @catch (NSException *exception) {
        YYBMacLogError(kTag, @"Failed to record app launch: %@", exception.reason);
    }
}

+ (NSArray<YYBAppLaunchHistoryItem *> *)getLaunchHistory:(NSInteger)maxCount {
    NSArray<YYBAppLaunchHistoryItem *> *historyList = [self getLaunchHistoryFromStorage];
    
    if (maxCount > 0 && historyList.count > maxCount) {
        return [historyList subarrayWithRange:NSMakeRange(0, maxCount)];
    }
    
    return historyList;
}

+ (NSArray<YYBAppLaunchHistoryItem *> *)getRecentApps:(NSInteger)maxCount {
    NSArray<YYBAppLaunchHistoryItem *> *historyList = [self getLaunchHistoryFromStorage];
    NSMutableArray<YYBAppLaunchHistoryItem *> *recentApps = [NSMutableArray array];
    NSMutableSet<NSString *> *addedPkgNames = [NSMutableSet set];
    
    // 去重，保留最新的记录
    for (YYBAppLaunchHistoryItem *item in historyList) {
        if (![addedPkgNames containsObject:item.pkgName]) {
            [recentApps addObject:item];
            [addedPkgNames addObject:item.pkgName];
            
            if (maxCount > 0 && recentApps.count >= maxCount) {
                break;
            }
        }
    }
    
    return [recentApps copy];
}

+ (void)clearHistoryForApp:(NSString *)pkgName {
    if (!pkgName.length) {
        return;
    }
    
    NSMutableArray<YYBAppLaunchHistoryItem *> *historyList = [[self getLaunchHistoryFromStorage] mutableCopy];
    
    // 移除指定App的所有记录
    for (NSInteger i = historyList.count - 1; i >= 0; i--) {
        YYBAppLaunchHistoryItem *item = historyList[i];
        if ([item.pkgName isEqualToString:pkgName]) {
            [historyList removeObjectAtIndex:i];
        }
    }
    
    [self saveHistoryToStorage:historyList];
    YYBMacLogInfo(kTag, @"Cleared history for app: %@", pkgName);
}

+ (void)clearAllHistory {
    [[YYBMacMMKV sharedInstance] setString:@"" forKey:kLaunchHistoryKey];
    YYBMacLogInfo(kTag, @"Cleared all launch history");
}

+ (NSArray<YYBAppLaunchHistoryItem *> *)getLaunchHistoryFromStorage {
    NSString *jsonString = [[YYBMacMMKV sharedInstance] getStringForKey:kLaunchHistoryKey];
    if (!jsonString.length) {
        return @[];
    }
    
    @try {
        NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
        NSArray *dictArray = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:nil];
        
        if (![dictArray isKindOfClass:[NSArray class]]) {
            return @[];
        }
        
        NSMutableArray<YYBAppLaunchHistoryItem *> *historyList = [NSMutableArray array];
        for (NSDictionary *dict in dictArray) {
            if ([dict isKindOfClass:[NSDictionary class]]) {
                YYBAppLaunchHistoryItem *item = [YYBAppLaunchHistoryItem itemWithDictionary:dict];
                [historyList addObject:item];
            }
        }
        
        return [historyList copy];
        
    } @catch (NSException *exception) {
        YYBMacLogError(kTag, @"Failed to parse launch history from storage: %@", exception.reason);
        return @[];
    }
}

+ (void)saveHistoryToStorage:(NSArray<YYBAppLaunchHistoryItem *> *)historyList {
    @try {
        NSMutableArray *dictArray = [NSMutableArray array];
        for (YYBAppLaunchHistoryItem *item in historyList) {
            [dictArray addObject:[item toDictionary]];
        }
        
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dictArray options:0 error:nil];
        NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
        
        if (jsonString) {
            [[YYBMacMMKV sharedInstance] setString:jsonString forKey:kLaunchHistoryKey];
        }
        
    } @catch (NSException *exception) {
        YYBMacLogError(kTag, @"Failed to save launch history to storage: %@", exception.reason);
    }
}

@end
