# YYBAppLaunchHistory 使用说明

## 功能概述

`YYBAppLaunchHistory` 是一个优雅的App启动历史记录管理器，用于记录和管理用户打开App的历史记录。主要特性包括：

- **自动记录**：在 `openApp:` 方法中自动记录App启动历史
- **按时间排序**：历史记录按照启动时间倒序排列（最新的在前）
- **启动次数统计**：记录每个App的启动次数
- **本地存储**：使用MMKV进行高效的本地存储
- **数据去重**：支持获取去重的最近启动App列表
- **容量限制**：自动限制历史记录数量（默认500条）

## 核心类说明

### YYBAppLaunchHistoryItem
历史记录项，包含以下属性：
- `pkgName`: 包名
- `appName`: 应用名称  
- `iconUrl`: 图标URL
- `launchTime`: 启动时间
- `launchCount`: 启动次数

### YYBAppLaunchHistory
历史记录管理器，提供静态方法进行历史记录操作。

## 使用方法

### 1. 自动记录（已集成）
当调用 `[[YYBApkPackage shared] openApp:pkgName]` 时，会自动记录启动历史。

### 2. 获取历史记录

```objc
// 获取最近10条启动历史（包含重复启动）
NSArray<YYBAppLaunchHistoryItem *> *history = [[YYBApkPackage shared] getLaunchHistory:10];

// 获取所有历史记录
NSArray<YYBAppLaunchHistoryItem *> *allHistory = [[YYBApkPackage shared] getLaunchHistory:0];
```

### 3. 获取最近启动的App（去重）

```objc
// 获取最近启动的5个不同App
NSArray<YYBAppLaunchHistoryItem *> *recentApps = [[YYBApkPackage shared] getRecentApps:5];

// 遍历显示
for (YYBAppLaunchHistoryItem *item in recentApps) {
    NSLog(@"App: %@, 启动时间: %@, 启动次数: %ld", 
          item.appName, item.launchTime, (long)item.launchCount);
}
```

### 4. 获取启动次数

```objc
// 获取指定App的总启动次数
NSInteger count = [[YYBApkPackage shared] getLaunchCountForApp:@"com.example.app"];
NSLog(@"该App总共启动了 %ld 次", (long)count);
```

### 5. 清除历史记录

```objc
// 清除指定App的历史记录
[[YYBApkPackage shared] clearHistoryForApp:@"com.example.app"];

// 清除所有历史记录
[[YYBApkPackage shared] clearAllHistory];
```

## 实际应用场景

### 1. 最近使用的App列表
```objc
- (void)updateRecentAppsUI {
    NSArray<YYBAppLaunchHistoryItem *> *recentApps = [[YYBApkPackage shared] getRecentApps:8];
    
    // 更新UI显示最近使用的App
    [self.recentAppsCollectionView reloadWithData:recentApps];
}
```

### 2. App使用频率统计
```objc
- (void)showAppUsageStatistics {
    NSArray<YYBAppLaunchHistoryItem *> *recentApps = [[YYBApkPackage shared] getRecentApps:0];
    
    // 按启动次数排序
    NSArray *sortedApps = [recentApps sortedArrayUsingComparator:^NSComparisonResult(YYBAppLaunchHistoryItem *obj1, YYBAppLaunchHistoryItem *obj2) {
        return [@(obj2.launchCount) compare:@(obj1.launchCount)];
    }];
    
    // 显示使用频率最高的App
    for (YYBAppLaunchHistoryItem *item in [sortedApps subarrayWithRange:NSMakeRange(0, MIN(5, sortedApps.count))]) {
        NSLog(@"热门App: %@, 启动次数: %ld", item.appName, (long)item.launchCount);
    }
}
```

### 3. 搜索建议
```objc
- (NSArray *)getSearchSuggestions:(NSString *)keyword {
    NSArray<YYBAppLaunchHistoryItem *> *recentApps = [[YYBApkPackage shared] getRecentApps:0];
    NSMutableArray *suggestions = [NSMutableArray array];
    
    for (YYBAppLaunchHistoryItem *item in recentApps) {
        if ([item.appName.lowercaseString containsString:keyword.lowercaseString]) {
            [suggestions addObject:item];
        }
    }
    
    return [suggestions copy];
}
```

## 数据存储

- 使用 `YYBMacMMKV` 进行本地存储
- 存储键：`YYBAppLaunchHistory`
- 数据格式：JSON数组
- 最大记录数：500条（可在代码中调整）

## 注意事项

1. **线程安全**：所有操作都是线程安全的
2. **异常处理**：包含完整的异常处理机制
3. **性能优化**：使用MMKV确保高性能读写
4. **内存管理**：自动限制历史记录数量，避免内存过度使用
5. **数据一致性**：启动时会更新App信息（名称、图标等）

## 扩展建议

如需要更多功能，可以考虑：
- 添加启动时长统计
- 支持按日期范围查询
- 添加App分类统计
- 支持导出历史数据
- 添加隐私模式（不记录特定App）
