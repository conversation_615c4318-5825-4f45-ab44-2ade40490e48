# YYBAppLaunchHistory 设计方案

## 概述

为了满足在App打开的公共接口处记录用户打开App历史记录的需求，我设计了一个优雅的历史记录管理系统。该系统具有以下特点：

- **自动记录**：在 `openApp:` 方法中自动记录
- **按时间排序**：历史记录按打开时间新旧排序
- **本地存储**：使用 YYBMacMMKV 进行高效本地存储
- **线程安全**：支持多线程环境
- **性能优化**：限制历史记录数量，避免无限增长

## 架构设计

### 核心类结构

```
YYBAppLaunchHistory (管理器)
├── YYBAppLaunchHistoryItem (历史记录项)
├── 存储层 (YYBMacMMKV)
└── 业务层集成 (YYBApkPackage)
```

### 类职责分工

1. **YYBAppLaunchHistoryItem**
   - 封装单个历史记录项的数据
   - 提供数据转换方法（字典 ↔ 对象）
   - 包含包名、应用名、图标、启动时间、启动次数等信息

2. **YYBAppLaunchHistory**
   - 核心管理器，提供所有历史记录操作
   - 负责数据持久化和内存管理
   - 提供线程安全的操作接口

3. **YYBApkPackage 扩展**
   - 在现有业务类中集成历史记录功能
   - 提供便捷的业务层接口

## 实现细节

### 1. 数据模型

```objc
@interface YYBAppLaunchHistoryItem : NSObject
@property (nonatomic, strong) NSString *pkgName;        // 包名（主键）
@property (nonatomic, strong) NSString *appName;        // 应用名称
@property (nonatomic, strong) NSString *iconUrl;        // 图标URL
@property (nonatomic, strong) NSDate *launchTime;       // 启动时间
@property (nonatomic, assign) NSInteger launchCount;    // 启动次数
@end
```

### 2. 存储策略

- **存储介质**：YYBMacMMKV
- **存储键**：`YYBAppLaunchHistory`
- **数据格式**：JSON数组
- **容量限制**：最多500条记录
- **数据结构**：
  ```json
  [
    {
      "pkgName": "com.example.app",
      "appName": "示例应用",
      "iconUrl": "https://example.com/icon.png",
      "launchTime": 1692518400.0,
      "launchCount": 5
    }
  ]
  ```

### 3. 核心算法

#### 记录启动逻辑
```
1. 检查是否已存在该App的记录
2. 如果存在：
   - 更新启动时间为当前时间
   - 启动次数 +1
   - 移动到列表开头（保持时间排序）
3. 如果不存在：
   - 创建新记录
   - 插入到列表开头
4. 检查列表长度，超过限制则删除旧记录
5. 保存到本地存储
```

#### 时间排序策略
- 使用数组结构，最新记录在前
- 每次启动时将记录移动到数组开头
- 确保列表始终按最后启动时间倒序排列

### 4. 集成方式

在 `YYBApkPackage.mm` 的 `openApp:` 方法中添加一行代码：

```objc
- (void)openApp:(NSString *)pkgName {
    // ... 原有代码 ...
    
    // 记录打开历史
    [YYBAppLaunchHistory recordAppLaunch:info];
    
    // ... 原有代码 ...
}
```

## API 设计

### 核心接口

```objc
// 记录App启动（自动调用）
+ (void)recordAppLaunch:(InstallApkInfo *)apkInfo;

// 获取历史记录（按时间倒序）
+ (NSArray<YYBAppLaunchHistoryItem *> *)getLaunchHistory:(NSInteger)maxCount;

// 获取最近App列表（去重）
+ (NSArray<YYBAppLaunchHistoryItem *> *)getRecentApps:(NSInteger)maxCount;

// 获取启动次数
+ (NSInteger)getLaunchCountForApp:(NSString *)pkgName;

// 清除历史记录
+ (void)clearHistoryForApp:(NSString *)pkgName;
+ (void)clearAllHistory;
```

### 业务层接口

```objc
// 通过 YYBApkPackage 调用
[[YYBApkPackage shared] getLaunchHistory:10];
[[YYBApkPackage shared] getRecentApps:5];
[[YYBApkPackage shared] getLaunchCountForApp:@"com.example.app"];
```

## 使用场景

### 1. 最近使用App列表
```objc
NSArray *recentApps = [[YYBApkPackage shared] getRecentApps:8];
// 用于首页显示最近使用的App
```

### 2. App使用统计
```objc
NSInteger count = [[YYBApkPackage shared] getLaunchCountForApp:pkgName];
// 显示App使用频率
```

### 3. 搜索建议
```objc
NSArray *recentApps = [[YYBApkPackage shared] getRecentApps:0];
// 根据历史记录提供搜索建议
```

## 优势特点

### 1. 优雅集成
- 最小化侵入性：只需在 `openApp:` 方法中添加一行代码
- 无需修改现有业务逻辑
- 向后兼容，不影响现有功能

### 2. 高性能
- 使用 MMKV 确保高效读写
- 内存中维护有序列表，查询快速
- 自动限制数据量，避免性能问题

### 3. 数据安全
- 完整的异常处理机制
- 线程安全操作
- 数据格式向前兼容

### 4. 易于扩展
- 模块化设计，便于添加新功能
- 清晰的接口定义
- 支持自定义配置

## 测试验证

提供了完整的测试用例 `YYBAppLaunchHistory_Test.mm`，包括：
- 记录启动测试
- 历史记录获取测试
- 启动次数统计测试
- 数据清除测试

## 部署建议

1. **渐进式部署**：先在测试环境验证功能
2. **数据迁移**：如有现有历史数据，提供迁移方案
3. **监控指标**：监控存储空间使用和性能表现
4. **用户反馈**：收集用户对历史记录功能的反馈

## 未来扩展

可以考虑的扩展功能：
- 启动时长统计
- 按日期范围查询
- App分类统计
- 数据导出功能
- 隐私模式支持
