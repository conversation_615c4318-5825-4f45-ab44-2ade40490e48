//
//  YYBAppLaunchHistory_Test.mm
//  YYBMacApp
//
//  Created by AI Assistant on 2025/8/20.
//  测试文件，用于验证历史记录功能
//

#import "YYBAppLaunchHistory.h"
#import "InstallApkInfo.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

static NSString *const kTestTag = @"YYBAppLaunchHistory_Test";

@implementation YYBAppLaunchHistory (Test)

+ (void)runTests {
    YYBMacLogInfo(kTestTag, @"开始测试 YYBAppLaunchHistory");
    
    // 清除所有历史记录，确保测试环境干净
    [YYBAppLaunchHistory clearAllHistory];
    
    // 测试1: 记录App启动
    [self testRecordAppLaunch];
    
    // 测试2: 获取历史记录
    [self testGetLaunchHistory];
    
    // 测试3: 获取最近App列表
    [self testGetRecentApps];
    
    // 测试4: 清除历史记录
    [self testClearHistory];
    
    YYBMacLogInfo(kTestTag, @"YYBAppLaunchHistory 测试完成");
}

+ (void)testRecordAppLaunch {
    YYBMacLogInfo(kTestTag, @"测试记录App启动");
    
    // 创建测试App信息
    InstallApkInfo *app1 = [[InstallApkInfo alloc] init];
    app1.pkgName = @"com.test.app1";
    app1.name = @"测试应用1";
    app1.iconUrl = @"https://example.com/icon1.png";
    
    InstallApkInfo *app2 = [[InstallApkInfo alloc] init];
    app2.pkgName = @"com.test.app2";
    app2.name = @"测试应用2";
    app2.iconUrl = @"https://example.com/icon2.png";
    
    // 记录启动
    [YYBAppLaunchHistory recordAppLaunch:app1];
    [YYBAppLaunchHistory recordAppLaunch:app2];
    [YYBAppLaunchHistory recordAppLaunch:app1]; // 再次启动app1
    
    YYBMacLogInfo(kTestTag, @"记录了3次App启动");
}

+ (void)testGetLaunchHistory {
    YYBMacLogInfo(kTestTag, @"测试获取历史记录");
    
    NSArray<YYBAppLaunchHistoryItem *> *history = [YYBAppLaunchHistory getLaunchHistory:0];
    YYBMacLogInfo(kTestTag, @"获取到 %ld 条历史记录", (long)history.count);
    
    for (YYBAppLaunchHistoryItem *item in history) {
        YYBMacLogInfo(kTestTag, @"历史记录: %@ - %@",
                      item.pkgName, item.appName);
    }
    
    // 测试限制数量
    NSArray<YYBAppLaunchHistoryItem *> *limitedHistory = [YYBAppLaunchHistory getLaunchHistory:2];
    YYBMacLogInfo(kTestTag, @"限制获取2条记录，实际获取到 %ld 条", (long)limitedHistory.count);
}

+ (void)testGetRecentApps {
    YYBMacLogInfo(kTestTag, @"测试获取最近App列表");
    
    NSArray<YYBAppLaunchHistoryItem *> *recentApps = [YYBAppLaunchHistory getRecentApps:0];
    YYBMacLogInfo(kTestTag, @"获取到 %ld 个最近使用的App", (long)recentApps.count);
    
    for (YYBAppLaunchHistoryItem *item in recentApps) {
        YYBMacLogInfo(kTestTag, @"最近App: %@ - %@ (最后启动: %@)",
                      item.pkgName, item.appName, item.launchTime);
    }
}

+ (void)testClearHistory {
    YYBMacLogInfo(kTestTag, @"测试清除历史记录");
    
    // 清除指定App的历史记录
    [YYBAppLaunchHistory clearHistoryForApp:@"com.test.app1"];
    
    NSArray<YYBAppLaunchHistoryItem *> *historyAfterClear = [YYBAppLaunchHistory getLaunchHistory:0];
    YYBMacLogInfo(kTestTag, @"清除app1后，剩余历史记录数: %ld", (long)historyAfterClear.count);
    
    // 清除所有历史记录
    [YYBAppLaunchHistory clearAllHistory];
    
    NSArray<YYBAppLaunchHistoryItem *> *historyAfterClearAll = [YYBAppLaunchHistory getLaunchHistory:0];
    YYBMacLogInfo(kTestTag, @"清除所有记录后，剩余历史记录数: %ld", (long)historyAfterClearAll.count);
}

@end

// 使用示例函数
void demonstrateAppLaunchHistory() {
    YYBMacLogInfo(kTestTag, @"=== YYBAppLaunchHistory 使用示例 ===");
    
    // 模拟用户打开App
    InstallApkInfo *wechatApp = [[InstallApkInfo alloc] init];
    wechatApp.pkgName = @"com.tencent.xinWeChat";
    wechatApp.name = @"微信";
    wechatApp.iconUrl = @"https://example.com/wechat.png";
    
    InstallApkInfo *qqApp = [[InstallApkInfo alloc] init];
    qqApp.pkgName = @"com.tencent.qq";
    qqApp.name = @"QQ";
    qqApp.iconUrl = @"https://example.com/qq.png";
    
    // 记录启动历史
    [YYBAppLaunchHistory recordAppLaunch:wechatApp];
    [YYBAppLaunchHistory recordAppLaunch:qqApp];
    [YYBAppLaunchHistory recordAppLaunch:wechatApp]; // 再次启动微信
    
    // 获取最近使用的App（用于UI显示）
    NSArray<YYBAppLaunchHistoryItem *> *recentApps = [YYBAppLaunchHistory getRecentApps:5];
    YYBMacLogInfo(kTestTag, @"最近使用的App:");
    for (YYBAppLaunchHistoryItem *item in recentApps) {
        YYBMacLogInfo(kTestTag, @"  %@", item.appName);
    }
}
