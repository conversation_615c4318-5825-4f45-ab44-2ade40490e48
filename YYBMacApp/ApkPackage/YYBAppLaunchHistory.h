//
//  YYBAppLaunchHistory.h
//  YYBMacApp
//
//  Created by lichenlin on 2025/8/20.
//

#import <Foundation/Foundation.h>

@class InstallApkInfo;

NS_ASSUME_NONNULL_BEGIN

/**
 * App启动历史记录项
 */
@interface YYBAppLaunchHistoryItem : NSObject

@property (nonatomic, strong) InstallApkInfo *launchApkInfo;    ///< 完整的apk信息
@property (nonatomic, strong) NSDate *launchTime;               ///< 启动时间

// 便捷访问属性（从launchApkInfo中获取）
@property (nonatomic, readonly) NSString *pkgName;              ///< 包名
@property (nonatomic, readonly) NSString *appName;              ///< 应用名称
@property (nonatomic, readonly) NSString *iconUrl;              ///< 图标URL

/**
 * 从InstallApkInfo创建历史记录项
 */
+ (instancetype)itemWithApkInfo:(InstallApkInfo *)apkInfo;

/**
 * 从字典创建历史记录项（用于从MMKV读取）
 */
+ (instancetype)itemWithDictionary:(NSDictionary *)dict;

/**
 * 转换为字典（用于存储到MMKV）
 */
- (NSDictionary *)toDictionary;

@end

/**
 * App启动历史记录管理器
 * 负责记录和管理用户打开App的历史记录
 */
@interface YYBAppLaunchHistory : NSObject

/**
 * 记录App启动
 * @param apkInfo App信息
 */
+ (void)recordAppLaunch:(InstallApkInfo *)apkInfo;

/**
 * 获取启动历史记录列表（按时间倒序）
 * @param maxCount 最大返回数量，0表示返回全部
 * @return 历史记录数组
 */
+ (NSArray<YYBAppLaunchHistoryItem *> *)getLaunchHistory:(NSInteger)maxCount;

/**
 * 获取最近启动的App列表（去重，按最后启动时间倒序）
 * @param maxCount 最大返回数量，0表示返回全部
 * @return 历史记录数组
 */
+ (NSArray<YYBAppLaunchHistoryItem *> *)getRecentApps:(NSInteger)maxCount;

/**
 * 清除指定App的历史记录
 * @param pkgName 包名
 */
+ (void)clearHistoryForApp:(NSString *)pkgName;

/**
 * 清除所有历史记录
 */
+ (void)clearAllHistory;

@end

NS_ASSUME_NONNULL_END
